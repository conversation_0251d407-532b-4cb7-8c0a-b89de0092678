{"folders": [{"name": "root", "path": "."}, {"path": "sentry-ruby"}, {"path": "sentry-rails"}, {"path": "sentry-sidekiq"}, {"path": "sentry-delayed_job"}, {"path": "sentry-resque"}, {"path": "sentry-opentelemetry"}], "settings": {"files.exclude": {"sentry-delayed_job": true, "sentry-rails": true, "sentry-resque": true, "sentry-ruby": true, "sentry-sidekiq": true, "sentry-opentelemetry": true}, "editor.formatOnSaveMode": "file", "editor.formatOnSave": true, "rubyLsp.rubyVersionManager": {"identifier": "auto"}, "rubyLsp.formatter": "auto"}, "extensions": {"recommendations": ["Shopify.ruby-lsp"]}}