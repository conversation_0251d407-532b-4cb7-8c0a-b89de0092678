minVersion: '1.8.1'
changelogPolicy: simple
preReleaseCommand: ruby .scripts/batch_release.rb
requireNames:
  - /^sentry-ruby-.*\.gem$/
  - /^sentry-ruby-core-.*\.gem$/
  - /^sentry-rails-.*\.gem$/
  - /^sentry-sidekiq-.*\.gem$/
  - /^sentry-resque-.*\.gem$/
  - /^sentry-delayed_job-.*\.gem$/
  - /^sentry-opentelemetry-.*\.gem$/
targets:
    - name: gem
    - name: registry
      sdks:
        'gem:sentry-ruby':
        'gem:sentry-ruby-core':
        'gem:sentry-rails':
        'gem:sentry-sidekiq':
        'gem:sentry-delayed_job':
        'gem:sentry-opentelemetry':
        'gem:sentry-resque':
    - name: github
