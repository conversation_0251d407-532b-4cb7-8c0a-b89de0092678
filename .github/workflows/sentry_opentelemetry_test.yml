name: sentry-opentelemetry Test

on:
  workflow_dispatch:
  workflow_call:
    outputs:
      matrix-result:
        description: "Matrix job result"
        value: ${{ jobs.test.outputs.matrix-result }}
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-opentelemetry-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-opentelemetry
    name: Ruby ${{ matrix.ruby_version }} & OpenTelemetry ${{ matrix.opentelemetry_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-opentelemetry/Gemfile
      BUNDLE_WITHOUT: rubocop
      OPENTELEMETRY_VERSION: ${{ matrix.opentelemetry_version }}
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ fromJson(inputs.versions) }}
        # opentelemetry_version: [1.2.0]
        include:
          - ruby_version: 3.2
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
        exclude:
          - { ruby_version: 'jruby-head' }
    steps:
      - uses: actions/checkout@v4

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Run specs
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
