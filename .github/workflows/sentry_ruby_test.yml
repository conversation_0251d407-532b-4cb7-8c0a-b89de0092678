name: sentry-ruby Test

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-ruby-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-ruby
    name: Ruby ${{ matrix.ruby_version }} & Rack ${{ matrix.rack_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-ruby/Gemfile
      BUNDLE_WITHOUT: rubocop
      RACK_VERSION: ${{ matrix.rack_version }}
      REDIS_RB_VERSION: ${{ matrix.redis_rb_version }}
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ from<PERSON>son(inputs.versions) }}
        rack_version: [2.0, 3.0, 3.1]
        redis_rb_version: [4.0]
        include:
          - ruby_version: 3.2
            rack_version: 0
            redis_rb_version: 5.0
          - ruby_version: 3.2
            rack_version: 2.0
            redis_rb_version: 5.0
          - ruby_version: 3.2
            rack_version: 3.0
            redis_rb_version: 5.0
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
          - ruby_version: 3.2
            rack_version: 3.0
          - ruby_version: 3.3
            rack_version: 3.1
            redis_rb_version: 5.3
          - ruby_version: 3.4
            rack_version: 3.1
            redis_rb_version: 5.3
        exclude:
          - ruby_version: 'jruby'
          - ruby_version: 'jruby-head'
    steps:
      - uses: actions/checkout@v4

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Start Redis
        uses: supercharge/redis-github-action@c169aa53af4cd5d9321e9114669dbd11be08d307
        with:
          redis-version: 6

      - name: Run specs with Rack ${{ matrix.rack_version }} and redis-rb ${{ matrix.redis_rb_version }}
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
