name: sentry-resque Test

on:
  workflow_dispatch:
  workflow_call:
    outputs:
      matrix-result:
        description: "Matrix job result"
        value: ${{ jobs.test.outputs.matrix-result }}
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-resque-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-resque
    name: Ruby ${{ matrix.ruby_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-resque/Gemfile
      BUNDLE_WITHOUT: rubocop
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ from<PERSON><PERSON>(inputs.versions) }}
        include:
          - ruby_version: "3.2"
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
        exclude:
          - ruby_version: 'jruby'
          - ruby_version: 'jruby-head'
    steps:
      - uses: actions/checkout@v4
      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Start Redis
        uses: supercharge/redis-github-action@1.1.0
        with:
          redis-version: 5

      - name: Run specs without Rails
        env:
          RUBYOPT: ${{ matrix.options.rubyopt }}
        run: BUNDLE_WITHOUT="rubocop rails" bundle exec rake

      - name: Run specs with Rails
        env:
          RUBYOPT: ${{ matrix.options.rubyopt }}
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
