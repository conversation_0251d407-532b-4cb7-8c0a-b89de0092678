name: sentry-delayed_job Test

on:
  workflow_dispatch:
  workflow_call:
    outputs:
      matrix-result:
        description: "Matrix job result"
        value: ${{ jobs.test.outputs.matrix-result }}
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-delayed-job-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-delayed_job
    name: Ruby ${{ matrix.ruby_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-delayed_job/Gemfile
      BUNDLE_WITHOUT: rubocop
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ from<PERSON><PERSON>(inputs.versions) }}
        include:
          - ruby_version: "3.2"
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
        exclude:
          # Because Rails 7.0 currently doesn't work with Ruby head
          # LoadError:
          #  cannot load such file -- mutex_m
          - { ruby_version: "head" }
          - { ruby_version: 'jruby-head' }
    steps:
      - uses: actions/checkout@v4
      - name: Install sqlite
        run: |
          # See https://github.community/t5/GitHub-Actions/ubuntu-latest-Apt-repository-list-issues/td-p/41122/page/2
          for apt_file in `grep -lr microsoft /etc/apt/sources.list.d/`; do sudo rm $apt_file; done
          sudo apt-get update
          sudo apt-get install libsqlite3-dev

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Run specs
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
