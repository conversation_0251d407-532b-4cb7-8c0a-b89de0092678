name: Build Batch Release

on:
  push:
    branches:
      - release/**
jobs:
  build:
    name: Build gems
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 3.4
    - name: Build gem source
      run:  ruby .scripts/batch_build.rb
    - name: Archive Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ github.sha }}
        path: sentry*/*.gem
