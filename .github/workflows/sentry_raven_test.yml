name: sentry-raven Test

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:
    paths:
      - "sentry-raven/**"
      - ".github/workflows/sentry_raven_test.yml"
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: sentry-raven-test-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-raven
    name: Test on ruby ${{ matrix.ruby_version }} and rails ${{ matrix.rails_version }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        rails_version: [0, 4.2, 5.2, 6.0.0]
        ruby_version: [2.3, 2.4, 2.5, 2.6, 2.7, "3.0", jruby-9.3]
        include:
          - ruby_version: "3.0"
            rails_version: 0
          - ruby_version: 2.7
            rails_version: 6.0.0
            env: RUBYOPT="--enable-frozen-string-literal --debug=frozen-string-literal"
        exclude:
          - ruby_version: 2.3
            rails_version: 6.0.0
          - ruby_version: 2.4
            rails_version: 6.0.0
          - ruby_version: 2.7
            rails_version: 4.2
          - ruby_version: jruby-9.3
            rails_version: 4.2
          - ruby_version: "3.0"
            rails_version: 4.2
          - ruby_version: "3.0"
            rails_version: 5.2
          - ruby_version: "3.0"
            rails_version: 6.0.0

    steps:
      - uses: actions/checkout@v4

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          bundler: 1
          ruby-version: ${{ matrix.ruby_version }}

      - name: Start Redis
        uses: supercharge/redis-github-action@1.1.0
        with:
          redis-version: 5

      - name: Build with Rails ${{ matrix.rails_version }}
        env:
          RAILS_VERSION: ${{ matrix.rails_version }}
        run: |
          bundle install --jobs 4 --retry 3
          bundle exec rake
