name: sentry-sidekiq Test

on:
  workflow_dispatch:
  workflow_call:
    outputs:
      matrix-result:
        description: "Matrix job result"
        value: ${{ jobs.test.outputs.matrix-result }}
    inputs:
      versions:
        required: true
        type: string
# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
jobs:
  test:
    defaults:
      run:
        working-directory: sentry-sidekiq
    name: Ruby ${{ matrix.ruby_version }} & Sidekiq ${{ matrix.sidekiq_version }}, options - ${{ toJson(matrix.options) }}
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      RUBYOPT: ${{ matrix.options.rubyopt }}
      BUNDLE_GEMFILE: ${{ github.workspace }}/sentry-sidekiq/Gemfile
      BUNDLE_WITHOUT: rubocop
      SIDEKIQ_VERSION: ${{ matrix.sidekiq_version }}
    strategy:
      fail-fast: false
      matrix:
        ruby_version: ${{ fromJson(inputs.versions) }}
        sidekiq_version: ["5.0", "6.5", "7.0"]
        include:
          - ruby_version: 2.4
            sidekiq_version: 5.0
          - ruby_version: 2.5
            sidekiq_version: 5.0
          - ruby_version: 2.5
            sidekiq_version: 6.0
          - ruby_version: 2.6
            sidekiq_version: 5.0
          - ruby_version: 2.6
            sidekiq_version: 6.0
          - ruby_version: jruby-9.4.12.0
            sidekiq_version: 5.0
          - ruby_version: jruby-9.4.12.0
            sidekiq_version: 6.0
          - ruby_version: jruby-9.4.12.0
            sidekiq_version: 7.0
          - ruby_version: "3.2"
            sidekiq_version: 7.0
          - ruby_version: "3.2"
            sidekiq_version: 8.0.0
          - ruby_version: "3.3"
            sidekiq_version: 8.0.0
          - ruby_version: "3.4"
            sidekiq_version: 8.0.0
            options:
              rubyopt: "--enable-frozen-string-literal --debug=frozen-string-literal"
        exclude:
          - ruby_version: head
          - ruby_version: jruby
          - ruby_version: jruby-head
    steps:
      - uses: actions/checkout@v4

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Start Redis
        uses: supercharge/redis-github-action@1.1.0
        with:
          redis-version: ${{ (contains(matrix.sidekiq_version, '7.0') || contains(matrix.sidekiq_version, '8.0')) && 6 || 5 }}

      - name: Run specs with Sidekiq ${{ matrix.sidekiq_version }}
        env:
          WITH_SENTRY_RAILS: 1
        run: bundle exec rake

      - name: Upload Coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
