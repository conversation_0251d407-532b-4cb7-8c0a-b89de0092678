{"version": 1.2, "mode": "wall", "interval": 9900.990099009901, "samples": 15, "gc_samples": 0, "missed_samples": 0, "metadata": {}, "frames": {"140370219074600": {"name": "Integer#times", "file": "<cfunc>", "line": null, "total_samples": 5, "samples": 2}, "140370222379560": {"name": "Bar::Foo.foo", "file": "<dir>/spec/sentry/profiler_spec.rb", "line": 7, "total_samples": 5, "samples": 2}, "140370222379480": {"name": "Bar.bar", "file": "<dir>/spec/sentry/profiler_spec.rb", "line": 12, "total_samples": 15, "samples": 0}, "140370262463760": {"name": "block (4 levels) in <top (required)>", "file": "<dir>/spec/sentry/profiler_spec.rb", "line": 169, "total_samples": 15, "samples": 0}, "140370219081000": {"name": "BasicObject#instance_exec", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140369934628760": {"name": "RSpec::Core::Example#instance_exec", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 456, "total_samples": 15, "samples": 0}, "140370262209000": {"name": "RSpec::Core::Hooks::BeforeHook#run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 364, "total_samples": 15, "samples": 0}, "140370262206960": {"name": "RSpec::Core::Hooks::HookCollections#run_owned_hooks_for", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 527, "total_samples": 15, "samples": 0}, "140370219016480": {"name": "Array#each", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140370262206440": {"name": "RSpec::Core::Hooks::HookCollections#run_example_hooks_for", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 613, "total_samples": 15, "samples": 0}, "140370219016360": {"name": "Array#reverse_each", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140370262207720": {"name": "RSpec::Core::Hooks::HookCollections#run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 475, "total_samples": 15, "samples": 0}, "140369934628120": {"name": "RSpec::Core::Example#run_before_example", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 503, "total_samples": 15, "samples": 0}, "140369934656240": {"name": "RSpec::Core::Example#run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 246, "total_samples": 15, "samples": 0}, "140369934628080": {"name": "RSpec::Core::Example#with_around_and_singleton_context_hooks", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 508, "total_samples": 15, "samples": 0}, "140369934628640": {"name": "RSpec::Core::Example#with_around_example_hooks", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 466, "total_samples": 15, "samples": 0}, "140370262206400": {"name": "RSpec::Core::Hooks::HookCollections#run_around_example_hooks_for", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 619, "total_samples": 15, "samples": 0}, "140369934629600": {"name": "RSpec::Core::Example::Procsy#call", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 350, "total_samples": 15, "samples": 0}, "140369936132760": {"name": "RSpec::Retry#run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-retry-0.6.2/lib/rspec/retry.rb", "line": 107, "total_samples": 15, "samples": 0}, "140370203515000": {"name": "Kernel#loop", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140369936134000": {"name": "RSpec::Core::Example::Procsy#run_with_retry", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-retry-0.6.2/lib/rspec_ext/rspec_ext.rb", "line": 11, "total_samples": 15, "samples": 0}, "140369936133560": {"name": "RSpec::Retry.setup", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-retry-0.6.2/lib/rspec/retry.rb", "line": 7, "total_samples": 15, "samples": 0}, "140370262208320": {"name": "RSpec::Core::Hooks::AroundHook#execute_with", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/hooks.rb", "line": 389, "total_samples": 15, "samples": 0}, "140369934629640": {"name": "RSpec::Core::Example::Procsy#call", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example.rb", "line": 350, "total_samples": 15, "samples": 0}, "140370221599800": {"name": "RSpec::Core::ExampleGroup.run_examples", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example_group.rb", "line": 641, "total_samples": 15, "samples": 0}, "140370219015440": {"name": "Array#map", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140370221599880": {"name": "RSpec::Core::ExampleGroup.run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/example_group.rb", "line": 599, "total_samples": 15, "samples": 0}, "140369934821040": {"name": "RSpec::Core::Runner#run_specs", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/runner.rb", "line": 113, "total_samples": 15, "samples": 0}, "140370221475080": {"name": "RSpec::Core::Configuration#with_suite_hooks", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/configuration.rb", "line": 2062, "total_samples": 15, "samples": 0}, "140370262307680": {"name": "RSpec::Core::Reporter#report", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/reporter.rb", "line": 71, "total_samples": 15, "samples": 0}, "140369934821120": {"name": "RSpec::Core::Runner#run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/runner.rb", "line": 85, "total_samples": 15, "samples": 0}, "140369934821200": {"name": "RSpec::Core::Runner.run", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/runner.rb", "line": 64, "total_samples": 15, "samples": 0}, "140369934821320": {"name": "RSpec::Core::Runner.invoke", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/lib/rspec/core/runner.rb", "line": 43, "total_samples": 15, "samples": 0}, "140369934947600": {"name": "<top (required)>", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/gems/rspec-core-3.11.0/exe/rspec", "total_samples": 15, "samples": 0}, "140370220922120": {"name": "Kernel#load", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140369934788840": {"name": "<main>", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/bin/rspec", "line": 1, "total_samples": 15, "samples": 0}, "140370219331600": {"name": "Kernel#eval", "file": "<cfunc>", "line": null, "total_samples": 15, "samples": 0}, "140369933326520": {"name": "<main>", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/bin/ruby_executable_hooks", "total_samples": 15, "samples": 0}, "140370218844600": {"name": "<main>", "file": "/Users/<USER>/.rvm/gems/ruby-3.0.0/bin/ruby_executable_hooks", "total_samples": 15, "samples": 0}, "140370219073720": {"name": "Integer#**", "file": "<cfunc>", "line": null, "total_samples": 1, "samples": 1}, "140370265410040": {"name": "Kernel#sleep", "file": "<cfunc>", "line": null, "total_samples": 10, "samples": 10}}, "raw": [62, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370222379560, 140370219074600, 1, 63, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370222379560, 140370219074600, 140370222379560, 1, 62, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370222379560, 140370219074600, 1, 64, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370222379560, 140370219074600, 140370222379560, 140370219073720, 1, 63, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370222379560, 140370219074600, 140370222379560, 1, 61, 140370218844600, 140369933326520, 140370219331600, 140369934788840, 140370220922120, 140369934947600, 140369934821320, 140369934821200, 140369934821120, 140369934821040, 140370262307680, 140369934821040, 140370221475080, 140369934821040, 140370219015440, 140369934821040, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370219015440, 140370221599880, 140370221599880, 140370221599800, 140370219015440, 140370221599800, 140369934656240, 140369934628080, 140369934628640, 140370262207720, 140370262206400, 140369934629640, 140370262206400, 140370262208320, 140369934628760, 140370219081000, 140369936133560, 140369936134000, 140369936132760, 140370203515000, 140369936132760, 140369934629600, 140370262206400, 140370262207720, 140369934628640, 140369934628080, 140369934656240, 140369934628120, 140370262207720, 140370262206440, 140370219016360, 140370262206440, 140370262206960, 140370219016480, 140370262206960, 140370262209000, 140369934628760, 140370219081000, 140370262463760, 140370222379480, 140370265410040, 10], "raw_sample_timestamps": [1295905330989, 1295905338595, 1295905348330, 1295905358336, 1295905368777, 1295905379129, 1295905388495, 1295905398492, 1295905407835, 1295905418826, 1295905428669, 1295905438609, 1295905447579, 1295905458008, 1295905468407], "raw_timestamp_deltas": [12403, 7531, 9689, 9986, 10420, 10338, 9320, 9949, 9269, 10920, 9770, 9864, 8899, 10354, 10323]}