#!/usr/bin/env ruby
# frozen_string_literal: true

require "bundler/setup"
require "debug"
require "sentry-ruby"

# You can add fixtures and/or initialization code here to make experimenting
# with your gem easier. You can also use a different console, if you like.

# (If you use this, don't forget to add pry to your Gemfile!)
# require "pry"
# Pry.start

# Sentry.init do |config|
#   config.dsn = 'https://<EMAIL>/5434472'
# end

require "irb"
IRB.start(__FILE__)
