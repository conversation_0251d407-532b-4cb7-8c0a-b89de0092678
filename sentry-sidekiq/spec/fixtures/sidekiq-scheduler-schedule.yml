:schedule:
  happy:
    cron: "* * * * *"
    class: "HappyWorkerForScheduler"
  happy_timezone:
    cron: "* * * * * Europe/Vienna"
    class: "HappyWorkerForSchedulerWithTimezone"
  manual:
    cron: "* * * * *"
    class: "SadWorkerWithCron"
  regularly_happy:
    every: "10 minutes"
    class: "EveryHappyWorker"
  reports:
    in: "2 hours"
    class: "ReportingWorker"
  VeryLongOuterModule::VeryVeryVeryVeryLongInnerModule::Job:
    cron: "* * * * *"
