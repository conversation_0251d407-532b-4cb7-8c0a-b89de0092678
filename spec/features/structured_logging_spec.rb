# frozen_string_literal: true

require 'net/http'
require 'uri'

RSpec.describe "Structured Logging", type: :feature do
  let(:structured_log_file) { "/workspace/sentry/log/sentry_debug_logs.log" }
  let(:rails_app_url) { "http://localhost:4000" }

  def read_structured_logs
    return [] unless File.exist?(structured_log_file)

    File.readlines(structured_log_file).map do |line|
      JSON.parse(line.strip)
    rescue JSON::ParserError
      nil
    end.compact
  end

  def clear_structured_logs
    File.write(structured_log_file, "") if File.exist?(structured_log_file)
  end

  def make_request(path)
    uri = URI("#{rails_app_url}#{path}")
    Net::HTTP.get_response(uri)
  end

  before(:each) do
    # Clear structured logs before each test
    clear_structured_logs
  end

  it "captures Rails application logs using DebugStructuredLogger" do
    # Make a direct request to the Rails mini app
    response = make_request("/posts")
    expect(response.code).to eq("200")

    # Wait a moment for logs to be written
    sleep(1)

    # Check that structured log events were captured from the Rails mini app
    logged_events = read_structured_logs
    expect(logged_events).not_to be_empty

    # Verify we have some log events with proper structure
    expect(logged_events.length).to be > 0

    # Check that events have the expected structure
    log_event = logged_events.first
    expect(log_event).to have_key("timestamp")
    expect(log_event).to have_key("level")
    expect(log_event).to have_key("message")
    expect(log_event).to have_key("attributes")
    expect(log_event["timestamp"]).to be_a(String)
  end

  it "captures logs from Rails mini app" do
    # Make a direct request to the Rails mini app posts endpoint
    response = make_request("/posts")
    expect(response.code).to eq("200")

    # Wait a moment for logs to be written
    sleep(1)

    # Look for log events from the Rails mini app
    logged_events = read_structured_logs

    # Should have logs from the Rails app
    expect(logged_events).not_to be_empty

    # Look for the specific log message from PostsController
    posts_log = logged_events.find { |log| log["message"] == "Posts index accessed" }
    expect(posts_log).not_to be_nil
    expect(posts_log["level"]).to eq("info")
    expect(posts_log["attributes"]["posts_count"]).to eq(2)
  end

  it "captures structured logs with proper format" do
    # Make a direct request to the Rails mini app
    response = make_request("/posts")
    expect(response.code).to eq("200")

    # Wait a moment for logs to be written
    sleep(1)

    # Check that structured log events were captured from the Rails mini app
    logged_events = read_structured_logs
    expect(logged_events).not_to be_empty

    # Verify the structure of log events
    log_event = logged_events.first
    expect(log_event).to have_key("timestamp")
    expect(log_event).to have_key("level")
    expect(log_event).to have_key("message")
    expect(log_event).to have_key("attributes")
    expect(log_event["timestamp"]).to be_a(String)
    expect(log_event["level"]).to be_a(String)
    expect(log_event["message"]).to be_a(String)
    expect(log_event["attributes"]).to be_a(Hash)
  end

  it "verifies Rails mini app is configured with DebugStructuredLogger" do
    # Check the health endpoint first
    response = make_request("/health")
    expect(response.code).to eq("200")

    health_data = JSON.parse(response.body)
    expect(health_data["sentry_initialized"]).to be true
    expect(health_data["structured_log_file_writable"]).to be true

    # Make a request to trigger logging
    make_request("/posts")

    # Wait for logs to be written
    sleep(1)

    logged_events = read_structured_logs
    expect(logged_events).not_to be_empty

    # Verify we have the expected log structure
    log_event = logged_events.first
    expect(log_event).to include("timestamp", "level", "message", "attributes")
  end
end
